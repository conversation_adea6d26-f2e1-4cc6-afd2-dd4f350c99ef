import React from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { Newspaper, TrendingUp, Building2, Plane } from "lucide-react";

const News = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const newsItems = [
    {
      icon: Building2,
      title: "Information Technology Investment Regions (ITIR)",
      content: "Information Technology Investment Regions (ITIR) is an integrated IT city with all physical and social infrastructure. Spread across 10000 acres of land in Devanahalli, Chickballapur, Dodhaballapur and nearby regions in north Bangalore.",
      highlight: "10,000 Acres Development"
    },
    {
      icon: Plane,
      title: "Aerospace SEZ in Devanahalli",
      content: "Aircraft maintenance, repair overhaul units to manufacture spare parts. Airbus, Lufthansa, CFM engines are some major plans showing interest.",
      highlight: "Major Aviation Hub"
    },
    {
      icon: TrendingUp,
      title: "Global Investors Meet Impact",
      content: "The Global Investors Meet brought the area into focus. Many investors have evinced keen interest in developing several IT Parks, SEZs, Star Hotels, Educational institutions, prestigious Residential Projects and many more.",
      highlight: "Investment Surge"
    },
    {
      icon: Newspaper,
      title: "Infrastructure Development",
      content: "Good news is that Bangalore is not far behind in infrastructure initiatives by Government. With increasing passenger traffic it is likely to become South India's Busiest Airport hub for both inbound and outbound (transient) traffic very soon.",
      highlight: "South India's Hub"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { x: -50, opacity: 0 },
    visible: {
      x: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <section id="news" className="py-20 bg-gradient-to-br from-accent-50 to-primary-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="text-center mb-16"
        >
          <motion.h2
            variants={itemVariants}
            className="text-section-mobile md:text-section font-heading font-bold text-primary mb-4"
          >
            North Bangalore News
          </motion.h2>
          <motion.div
            variants={itemVariants}
            className="w-20 h-1 bg-secondary mx-auto mb-6"
          />
          <motion.p
            variants={itemVariants}
            className="text-body-lg text-charcoal max-w-3xl mx-auto"
          >
            Stay updated with the latest developments and growth opportunities in North Bangalore's 
            rapidly expanding infrastructure and business landscape.
          </motion.p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="grid grid-cols-1 md:grid-cols-2 gap-8"
        >
          {newsItems.map((item, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="bg-white rounded-xl p-6 shadow-card hover:shadow-card-hover transition-all duration-300 group border-l-4 border-primary"
            >
              <div className="flex items-start space-x-4">
                <div className="bg-primary/10 text-primary p-3 rounded-lg group-hover:bg-primary group-hover:text-white transition-all duration-300">
                  <item.icon size={24} />
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-heading font-semibold text-primary group-hover:text-primary-700 transition-colors duration-300">
                      {item.title}
                    </h3>
                    <span className="bg-secondary/10 text-secondary text-xs px-3 py-1 rounded-full font-medium">
                      {item.highlight}
                    </span>
                  </div>
                  <p className="text-body-sm text-charcoal leading-relaxed">
                    {item.content}
                  </p>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default News;
