import React, { useState } from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import {
  Phone,
  Mail,
  MapPin,
  Clock,
  Send,
  CheckCircle,
  MessageCircle,
  Calendar,
  User,
  MessageSquare,
} from "lucide-react";

const Contact = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [formStep, setFormStep] = useState(1);
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    inquiryType: "",
    message: "",
    preferredContact: "",
    visitDate: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      setIsSubmitted(true);
    }, 2000);
  };

  const nextStep = () => {
    if (formStep < 3) {
      setFormStep(formStep + 1);
    }
  };

  const prevStep = () => {
    if (formStep > 1) {
      setFormStep(formStep - 1);
    }
  };

  const contactInfo = [
    {
      icon: Phone,
      title: "Phone",
      details: ["+91-9036699799", "+91-8151884545"],
      color: "bg-primary/20 text-primary",
    },
    {
      icon: Mail,
      title: "Email",
      details: ["<EMAIL>"],
      color: "bg-secondary/20 text-secondary",
    },
    {
      icon: MapPin,
      title: "Location",
      details: [
        "7-8/1, 4th Main, 4th block, Kalyan Nagar, Bengaluru, Karnataka 560043",
      ],
      color: "bg-primary/20 text-primary",
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  if (isSubmitted) {
    return (
      <section id="contact" className="py-20 bg-accent">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8 }}
            className="text-center space-y-8 bg-white rounded-2xl p-12 shadow-2xl"
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 300 }}
              className="w-24 h-24 bg-green-500/20 rounded-full flex items-center justify-center mx-auto"
            >
              <CheckCircle className="h-12 w-12 text-green-500" />
            </motion.div>

            <div className="space-y-4">
              <h2 className="text-heading-lg font-heading font-bold text-primary">
                Thank You!
              </h2>
              <p className="text-body text-charcoal font-montserrat">
                Your inquiry has been received. Our luxury real estate
                specialist will contact you within 24 hours to discuss your
                exclusive viewing appointment.
              </p>
            </div>

            <motion.button
              onClick={() => {
                setIsSubmitted(false);
                setFormStep(1);
                setFormData({
                  firstName: "",
                  lastName: "",
                  email: "",
                  phone: "",
                  inquiryType: "",
                  message: "",
                  preferredContact: "",
                  visitDate: "",
                });
              }}
              className="bg-primary hover:bg-primary/90 text-accent px-8 py-3 rounded-lg font-montserrat font-semibold transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Send Another Message
            </motion.button>
          </motion.div>
        </div>
      </section>
    );
  }

  return (
    <section id="contact" className="py-16 bg-gradient-to-br from-accent via-accent/95 to-accent/90 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-0 left-0 w-96 h-96 bg-primary rounded-full -translate-x-1/2 -translate-y-1/2"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-secondary rounded-full translate-x-1/2 translate-y-1/2"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="space-y-12"
        >
          {/* Header */}
          <motion.div variants={itemVariants} className="text-center space-y-6">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-4">
              <MessageCircle className="h-8 w-8 text-primary" />
            </div>
            <h2 className="text-section-mobile md:text-section font-heading font-bold text-primary">
              Get In Touch
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-primary to-secondary mx-auto rounded-full" />
            <p className="text-body-lg md:text-body-lg text-charcoal/80 font-body max-w-3xl mx-auto leading-relaxed">
              Ready to experience luxury living? Connect with our dedicated team of real estate specialists
              to schedule your private viewing or learn more about Signature Villa's exclusive offerings.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Information */}
            <motion.div variants={itemVariants} className="space-y-8">
              <div className="space-y-6">
                {contactInfo.map((info, index) => (
                  <motion.div
                    key={index}
                    className="group relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-500 border border-white/20"
                    whileHover={{ y: -4, scale: 1.02 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    {/* Gradient overlay on hover */}
                    <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-secondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                    <div className="relative flex items-start space-x-4">
                      <div
                        className={`w-14 h-14 rounded-2xl ${info.color} flex items-center justify-center flex-shrink-0 shadow-lg group-hover:scale-110 transition-transform duration-300`}
                      >
                        <info.icon className="h-7 w-7" />
                      </div>
                      <div className="space-y-2 flex-1">
                        <h4 className="text-xl font-playfair font-bold text-primary group-hover:text-primary/90 transition-colors">
                          {info.title}
                        </h4>
                        {info.details.map((detail, detailIndex) => (
                          <p
                            key={detailIndex}
                            className="text-charcoal/80 font-montserrat text-sm leading-relaxed"
                          >
                            {detail}
                          </p>
                        ))}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Quick Actions */}
              <div className="space-y-6">
                <h3 className="text-2xl font-playfair font-bold text-primary">
                  Quick Connect
                </h3>
                <div className="grid grid-cols-1 gap-4">
                  <motion.button
                    onClick={() => window.open("https://api.whatsapp.com/send?phone=919035055655&text=I am interested with shreyas properties", "_blank")}
                    className="group relative overflow-hidden flex items-center space-x-4 p-6 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300"
                    whileHover={{ scale: 1.03, y: -2 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    <div className="relative w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                      <MessageCircle className="h-6 w-6" />
                    </div>
                    <div className="relative text-left">
                      <div className="font-montserrat font-bold text-lg">WhatsApp Chat</div>
                      <div className="font-montserrat text-sm opacity-90">Get instant responses</div>
                    </div>
                  </motion.button>

                  <motion.button
                    onClick={() => window.open("tel:+919036699799")}
                    className="group relative overflow-hidden flex items-center space-x-4 p-6 bg-gradient-to-r from-primary to-primary/90 text-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300"
                    whileHover={{ scale: 1.03, y: -2 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    <div className="relative w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                      <Phone className="h-6 w-6" />
                    </div>
                    <div className="relative text-left">
                      <div className="font-montserrat font-bold text-lg">Call Now</div>
                      <div className="font-montserrat text-sm opacity-90">Speak with our experts</div>
                    </div>
                  </motion.button>
                </div>
              </div>
            </motion.div>

            {/* Multi-Step Form */}
            <motion.div
              variants={itemVariants}
              className="relative bg-white/90 backdrop-blur-sm rounded-3xl p-8 shadow-2xl border border-white/20"
            >
              {/* Form Header */}
              <div className="text-center mb-8">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-primary/10 rounded-full mb-4">
                  <Send className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-2xl font-playfair font-bold text-primary mb-2">
                  Send us a Message
                </h3>
                <p className="text-charcoal/70 font-montserrat">
                  Fill out the form below and we'll get back to you within 24 hours
                </p>
              </div>

              <div className="space-y-6">
                {/* Progress Bar */}
                <div className="flex items-center justify-between mb-8">
                  {[1, 2, 3].map((step) => (
                    <div key={step} className="flex items-center">
                      <motion.div
                        className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-montserrat font-bold transition-all duration-300 ${
                          step <= formStep
                            ? "bg-gradient-to-r from-primary to-secondary text-white shadow-lg"
                            : "bg-gray-200 text-gray-400"
                        }`}
                        whileHover={{ scale: 1.1 }}
                        animate={{ scale: step === formStep ? 1.1 : 1 }}
                      >
                        {step}
                      </motion.div>
                      {step < 3 && (
                        <div
                          className={`w-16 h-2 mx-2 rounded-full transition-all duration-500 ${
                            step < formStep ? "bg-gradient-to-r from-primary to-secondary" : "bg-gray-200"
                          }`}
                        />
                      )}
                    </div>
                  ))}
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Step 1: Personal Information */}
                  {formStep === 1 && (
                    <motion.div
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5 }}
                      className="space-y-4"
                    >
                      <h3 className="text-xl font-playfair font-semibold text-primary flex items-center">
                        <User className="h-5 w-5 mr-2" />
                        Personal Information
                      </h3>

                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-montserrat font-medium text-charcoal mb-2">
                            First Name *
                          </label>
                          <input
                            type="text"
                            name="firstName"
                            value={formData.firstName}
                            onChange={handleInputChange}
                            required
                            className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-primary/20 focus:border-primary transition-all duration-300 font-montserrat bg-gray-50/50 hover:bg-white"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-montserrat font-medium text-charcoal mb-2">
                            Last Name *
                          </label>
                          <input
                            type="text"
                            name="lastName"
                            value={formData.lastName}
                            onChange={handleInputChange}
                            required
                            className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-primary/20 focus:border-primary transition-all duration-300 font-montserrat bg-gray-50/50 hover:bg-white"
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-montserrat font-medium text-charcoal mb-2">
                          Email Address *
                        </label>
                        <input
                          type="email"
                          name="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          required
                          className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-primary/20 focus:border-primary transition-all duration-300 font-montserrat bg-gray-50/50 hover:bg-white"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-montserrat font-medium text-charcoal mb-2">
                          Phone Number *
                        </label>
                        <input
                          type="tel"
                          name="phone"
                          value={formData.phone}
                          onChange={handleInputChange}
                          required
                          className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-primary/20 focus:border-primary transition-all duration-300 font-montserrat bg-gray-50/50 hover:bg-white"
                        />
                      </div>
                    </motion.div>
                  )}

                  {/* Step 2: Inquiry Details */}
                  {formStep === 2 && (
                    <motion.div
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5 }}
                      className="space-y-4"
                    >
                      <h3 className="text-xl font-playfair font-semibold text-primary flex items-center">
                        <MessageSquare className="h-5 w-5 mr-2" />
                        Inquiry Details
                      </h3>

                      <div>
                        <label className="block text-sm font-montserrat font-medium text-charcoal mb-2">
                          Inquiry Type *
                        </label>
                        <select
                          name="inquiryType"
                          value={formData.inquiryType}
                          onChange={handleInputChange}
                          required
                          className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-primary/20 focus:border-primary transition-all duration-300 font-montserrat bg-gray-50/50 hover:bg-white"
                        >
                          <option value="">Select inquiry type</option>
                          <option value="purchase">Purchase Information</option>
                          <option value="viewing">Schedule Viewing</option>
                          <option value="investment">
                            Investment Opportunity
                          </option>
                          <option value="other">Other</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-montserrat font-medium text-charcoal mb-2">
                          Preferred Contact Method
                        </label>
                        <select
                          name="preferredContact"
                          value={formData.preferredContact}
                          onChange={handleInputChange}
                          className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-primary/20 focus:border-primary transition-all duration-300 font-montserrat bg-gray-50/50 hover:bg-white"
                        >
                          <option value="">Select preference</option>
                          <option value="email">Email</option>
                          <option value="phone">Phone Call</option>
                          <option value="whatsapp">WhatsApp</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-montserrat font-medium text-charcoal mb-2">
                          Preferred Visit Date
                        </label>
                        <input
                          type="date"
                          name="visitDate"
                          value={formData.visitDate}
                          onChange={handleInputChange}
                          className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-primary/20 focus:border-primary transition-all duration-300 font-montserrat bg-gray-50/50 hover:bg-white"
                        />
                      </div>
                    </motion.div>
                  )}

                  {/* Step 3: Message */}
                  {formStep === 3 && (
                    <motion.div
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5 }}
                      className="space-y-4"
                    >
                      <h3 className="text-xl font-playfair font-semibold text-primary flex items-center">
                        <Send className="h-5 w-5 mr-2" />
                        Your Message
                      </h3>

                      <div>
                        <label className="block text-sm font-montserrat font-medium text-charcoal mb-2">
                          Message
                        </label>
                        <textarea
                          name="message"
                          value={formData.message}
                          onChange={handleInputChange}
                          rows={6}
                          placeholder="Tell us about your requirements, timeline, or any specific questions..."
                          className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-primary/20 focus:border-primary transition-all duration-300 font-montserrat resize-none bg-gray-50/50 hover:bg-white"
                        />
                      </div>
                    </motion.div>
                  )}

                  {/* Navigation Buttons */}
                  <div className="flex justify-between pt-1">
                    {formStep > 1 && (
                      <motion.button
                        type="button"
                        onClick={prevStep}
                        className="px-8 py-4 border-2 border-primary text-primary rounded-xl font-montserrat font-bold hover:bg-primary hover:text-white transition-all duration-300 shadow-lg hover:shadow-xl"
                        whileHover={{ scale: 1.05, y: -2 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        Previous
                      </motion.button>
                    )}

                    {formStep < 3 ? (
                      <motion.button
                        type="button"
                        onClick={nextStep}
                        className="ml-auto px-8 py-4 bg-gradient-to-r from-primary to-secondary text-white rounded-xl font-montserrat font-bold hover:from-primary/90 hover:to-secondary/90 transition-all duration-300 shadow-lg hover:shadow-xl"
                        whileHover={{ scale: 1.05, y: -2 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        Next
                      </motion.button>
                    ) : (
                      <motion.button
                        type="submit"
                        disabled={isSubmitting}
                        className="ml-auto px-8 py-4 bg-gradient-to-r from-secondary to-secondary/90 text-primary rounded-xl font-montserrat font-bold hover:from-secondary/90 hover:to-secondary/80 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-3 shadow-lg hover:shadow-xl"
                        whileHover={{ scale: isSubmitting ? 1 : 1.05, y: isSubmitting ? 0 : -2 }}
                        whileTap={{ scale: isSubmitting ? 1 : 0.95 }}
                      >
                        {isSubmitting ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary" />
                            <span>Sending...</span>
                          </>
                        ) : (
                          <>
                            <Send className="h-4 w-4" />
                            <span>Send Message</span>
                          </>
                        )}
                      </motion.button>
                    )}
                  </div>
                </form>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Contact;
