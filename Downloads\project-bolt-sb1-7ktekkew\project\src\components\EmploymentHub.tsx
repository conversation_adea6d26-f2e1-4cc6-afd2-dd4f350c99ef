import React from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import {
  Building,
  MapPin,
  Users,
  TrendingUp,
  Clock,
  IndianRupee,
} from "lucide-react";

const EmploymentHub = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const employmentHubs = [
    {
      name: "Foxconn iPhone Campus",
      investment: "₹22,000 Crores",
      distance: "15 minutes",
      employees: "50,000+",
      type: "Manufacturing",
      description:
        "World's largest iPhone manufacturing facility outside China",
      icon: Building,
      color: "bg-blue-500/20 text-blue-600",
      status: "Under Development",
    },
    {
      name: "SAP Labs New Campus",
      investment: "₹1,500 Crores",
      distance: "18 minutes",
      employees: "15,000+",
      type: "IT Services",
      description: "Major software development and innovation center",
      icon: Building,
      color: "bg-green-500/20 text-green-600",
      status: "Expanding",
    },
    {
      name: "Infosys Facility",
      investment: "₹700 Crores",
      distance: "20 minutes",
      employees: "25,000+",
      type: "IT Services",
      description: "Large-scale IT services and development center",
      icon: Building,
      color: "bg-purple-500/20 text-purple-600",
      status: "Operational",
    },
    {
      name: "Aerospace Park",
      investment: "₹5,000 Crores",
      distance: "12 minutes",
      employees: "30,000+",
      type: "Aerospace",
      description: "India's largest aerospace and defense manufacturing hub",
      icon: Building,
      color: "bg-orange-500/20 text-orange-600",
      status: "Developing",
    },
  ];

  const investmentStats = [
    {
      icon: IndianRupee,
      value: "₹29,200+",
      label: "Crores Investment",
      description: "Total industrial investment in the region",
    },
    {
      icon: Users,
      value: "1,20,000+",
      label: "Employment Opportunities",
      description: "Direct and indirect job creation",
    },
    {
      icon: TrendingUp,
      value: "25%",
      label: "Annual Growth",
      description: "Property appreciation rate",
    },
    {
      icon: Clock,
      value: "20 mins",
      label: "Average Commute",
      description: "To major employment centers",
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  return (
    <section id="employment" className="py-8 bg-gradient-to-r from-primary to-secondary">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="space-y-8"
        >
          {/* Header */}
          <motion.div variants={itemVariants} className="text-center space-y-2">
          <h2 className="text-section-mobile md:text-section font-playfair font-bold text-white">
          Major Employment Hubs
            </h2>
            <div className="w-20 h-1 bg-secondary mx-auto" />
            <p className="text-body-mobile md:text-body text-white/90 font-montserrat max-w-3xl mx-auto">
            Devanahalli is at the center of North Bengaluru's industrial
              revolution, with major corporations investing over ₹29,000 Crores
              in world-class facilities, creating unprecedented employment and
              investment opportunities.
            </p>
          </motion.div>

          {/* Investment Statistics */}
          <motion.div variants={itemVariants}>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {investmentStats.map((stat, index) => (
                <motion.div
                  key={index}
                  className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center hover:bg-white/20 transition-all duration-300"
                  whileHover={{ y: -5 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <stat.icon className="h-8 w-8 text-secondary mx-auto mb-3" />
                  <div className="text-2xl font-playfair font-bold text-accent mb-1">
                    {stat.value}
                  </div>
                  <div className="text-sm font-montserrat text-accent/70 mb-2">
                    {stat.label}
                  </div>
                  <div className="text-xs font-montserrat text-accent/60">
                    {stat.description}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Employment Hubs Grid */}
          <motion.div variants={itemVariants}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {employmentHubs.map((hub, index) => (
                <motion.div
                  key={index}
                  className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 hover:bg-white/20 transition-all duration-300"
                  whileHover={{ scale: 1.02, y: -5 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <div className="flex items-start space-x-4">
                    <div className={`p-3 rounded-lg ${hub.color}`}>
                      <hub.icon className="h-6 w-6" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="text-xl font-playfair font-bold text-accent">
                          {hub.name}
                        </h3>
                        <span className="text-xs bg-secondary/20 text-secondary px-2 py-1 rounded-full">
                          {hub.status}
                        </span>
                      </div>
                      <p className="text-accent/80 font-montserrat mb-4">
                        {hub.description}
                      </p>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <div className="text-accent/60 font-montserrat">
                            Investment
                          </div>
                          <div className="text-secondary font-semibold">
                            {hub.investment}
                          </div>
                        </div>
                        <div>
                          <div className="text-accent/60 font-montserrat">
                            Distance
                          </div>
                          <div className="text-secondary font-semibold">
                            {hub.distance}
                          </div>
                        </div>
                        <div>
                          <div className="text-accent/60 font-montserrat">
                            Employment
                          </div>
                          <div className="text-secondary font-semibold">
                            {hub.employees}
                          </div>
                        </div>
                        <div>
                          <div className="text-accent/60 font-montserrat">
                            Sector
                          </div>
                          <div className="text-secondary font-semibold">
                            {hub.type}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Call to Action */}
          <motion.div variants={itemVariants} className="text-center">
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8">
              <h3 className="text-2xl font-playfair font-bold text-accent mb-4">
                Investment Opportunity of a Lifetime
              </h3>
              <p className="text-accent/80 font-montserrat mb-6 max-w-2xl mx-auto">
                With over ₹29,000 Crores of industrial investment and 1,20,000+
                employment opportunities being created in the region,
                Devanahalli represents the most promising real estate investment
                destination in North Bengaluru.
              </p>
              <motion.button
                className="bg-secondary hover:bg-secondary/90 text-primary px-8 py-4 rounded-lg font-montserrat font-semibold transition-all duration-300"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                Explore Investment Options
              </motion.button>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default EmploymentHub;
